html, body
{
    height: 0;
}
body {
    background-color: #FFFFFF;
}
.ui.menu .item img.logo {
    margin-right: 1.5em;
}
.ui.menu{
    border-radius: 0;
}

.ui.definition.table td:nth-child(2) {
    border-left: 0;
}

.main.container {
    width: 65em;
    margin-top: 6em;
    min-height: 100%;
    padding-top: 0;
    padding-right: 0.5em;
    padding-bottom: 3em;
    font-size: 16px;

}
.main.container .FooterPush{
    height: 7em;
}
.centerlog {
    display: block;
    margin: auto;
    height: 10rem;
}
.ui.search{
    margin-top: -2em;
}
.ui.input{
    text-align: center;
    display: block;
    margin: auto;

}
.ui.sbuttons{
    text-align: center;
    display: block;
    margin: auto;
    padding: 1em;
}
.ui.button{
    margin-top: 1em;
}
.prompt{
    width: 60em;
}
.ui.footer.segment {
    clear: both; /* 清除浮动元素格式 */
    position: relative;
    margin-top: -4em; /* <PERSON>er 高度的负值 */
    height: 4em;
}
.footer-inner{
    text-align: center;
}
.ui.stables{
    padding-top: 2em;

}